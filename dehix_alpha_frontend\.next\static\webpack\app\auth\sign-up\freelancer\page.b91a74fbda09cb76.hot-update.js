"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/sign-up/freelancer/page",{

/***/ "(app-pages-browser)/./src/components/form/register/freelancer.tsx":
/*!*****************************************************!*\
  !*** ./src/components/form/register/freelancer.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FreelancerPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(app-pages-browser)/./node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Briefcase,CheckCircle2,Eye,EyeOff,Loader2,LoaderCircle,Rocket,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _country_codes_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../country-codes.json */ \"(app-pages-browser)/./src/country-codes.json\");\n/* harmony import */ var _phoneNumberChecker__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./phoneNumberChecker */ \"(app-pages-browser)/./src/components/form/register/phoneNumberChecker.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_shared_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/shared/input */ \"(app-pages-browser)/./src/components/shared/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/axiosinstance */ \"(app-pages-browser)/./src/lib/axiosinstance.ts\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./src/components/ui/use-toast.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_shared_otpDialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/shared/otpDialog */ \"(app-pages-browser)/./src/components/shared/otpDialog.tsx\");\n/* harmony import */ var _components_DateOfBirthPicker_DateOfBirthPicker__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/DateOfBirthPicker/DateOfBirthPicker */ \"(app-pages-browser)/./src/components/DateOfBirthPicker/DateOfBirthPicker.tsx\");\n/* harmony import */ var _components_shared_FreelancerTermsDialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/shared/FreelancerTermsDialog */ \"(app-pages-browser)/./src/components/shared/FreelancerTermsDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n // Import the reusable TextInput component\n\n\n\n\n\n\n\n\n\nconst Stepper = (param)=>{\n    let { currentStep = 0 } = param;\n    const steps = [\n        {\n            id: 0,\n            title: \"Personal Info\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"]\n        },\n        {\n            id: 1,\n            title: \"Professional Info\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n        },\n        {\n            id: 2,\n            title: \"Verification\",\n            icon: _barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-5xl mx-auto py-4 sm:py-6 mb-10 sm:mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2 sm:space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold\",\n                        children: [\n                            \"Create Your Freelancer \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block\",\n                                children: \"Account\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 34\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Join our community and start your Freelancing Journey.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-4 text-center text-xs sm:text-sm\",\n                children: [\n                    \"Are you a business?\",\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        className: \"ml-2\",\n                        asChild: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/auth/sign-up/business\",\n                            children: \"Register Business\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center mt-4 sm:mt-8 px-2 sm:px-0\",\n                children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center rounded-full border-2 transition-all duration-300\\n                \".concat(currentStep > step.id ? \"bg-primary border-primary\" : currentStep === step.id ? \"border-primary bg-background text-primary\" : \"border-muted bg-background text-muted\"),\n                                        children: currentStep > step.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4 sm:w-6 sm:h-6 text-background\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                            className: \"w-4 h-4 sm:w-6 sm:h-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -bottom-6 left-1/2 -translate-x-1/2 text-xs sm:text-sm whitespace-nowrap font-medium\\n                \".concat(currentStep >= step.id ? \"text-primary\" : \"text-muted-foreground\"),\n                                        children: step.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, undefined),\n                            index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-20 sm:w-40 mx-2 sm:mx-4 h-[2px] bg-muted\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full bg-primary transition-all duration-500\",\n                                    style: {\n                                        width: currentStep > step.id ? \"100%\" : \"0%\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, step.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Stepper;\nconst getAgeWorkExperienceDifference = (today, dobDate)=>{\n    return today.getFullYear() - dobDate.getFullYear() - (today < new Date(today.getFullYear(), dobDate.getMonth(), dobDate.getDate()) ? 1 : 0);\n};\nconst profileFormSchema = zod__WEBPACK_IMPORTED_MODULE_22__.z.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().min(2, {\n        message: \"First Name must be at least 2 characters.\"\n    }),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().min(2, {\n        message: \"Last Name must be at least 2 characters.\"\n    }),\n    email: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().email({\n        message: \"Email must be a valid email address.\"\n    }),\n    userName: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().min(4, {\n        message: \"Username must be at least 4 characters long\"\n    }).max(20, {\n        message: \"Username must be less than 20 characters long\"\n    }).regex(/^[a-zA-Z0-9]{4}[a-zA-Z0-9_]*$/, {\n        message: \"Underscore allowed only after 4 letters/numbers\"\n    }),\n    phone: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().min(10, {\n        message: \"Phone number must be at least 10 digits.\"\n    }).regex(/^\\d+$/, {\n        message: \"Phone number can only contain digits.\"\n    }),\n    githubLink: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().optional().refine((value)=>!value || /^https?:\\/\\/(www\\.)?github\\.com\\/[a-zA-Z0-9_-]+\\/?$/.test(value), {\n        message: 'GitHub URL must start with \"https://github.com/\" or \"www.github.com/\" and have a valid username'\n    }),\n    linkedin: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().optional().refine((value)=>!value || /^https:\\/\\/www\\.linkedin\\.com\\/in\\/[a-zA-Z0-9_-]+\\/?$/.test(value), {\n        message: 'LinkedIn URL must start with \"https://www.linkedin.com/in/\" and have a valid username'\n    }),\n    personalWebsite: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().optional().refine((value)=>!value || /^(https?:\\/\\/|www\\.)[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}.*[a-zA-Z0-9].*$/.test(value), {\n        message: 'Invalid website URL. Must start with \"www.\" or \"https://\" and contain letters'\n    }),\n    password: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().min(6, {\n        message: \"Password must be at least 6 characters.\"\n    }),\n    perHourPrice: zod__WEBPACK_IMPORTED_MODULE_22__.z.number().max(300, \"Per hour price must not excedd 300\").refine((value)=>value >= 0, {\n        message: \"Price must be a non-negative number.\"\n    }),\n    referralCode: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().optional(),\n    workExperience: zod__WEBPACK_IMPORTED_MODULE_22__.z.number().min(0, \"Work experience must be at least 0 years\").max(60, \"Work experience must not exceed 60 years\"),\n    dob: zod__WEBPACK_IMPORTED_MODULE_22__.z.union([\n        zod__WEBPACK_IMPORTED_MODULE_22__.z.string(),\n        zod__WEBPACK_IMPORTED_MODULE_22__.z.date()\n    ]).optional().refine((value)=>{\n        if (!value) return true; // Allow empty (optional) field\n        const dobDate = new Date(value);\n        const today = new Date();\n        const minDate = new Date();\n        minDate.setFullYear(today.getFullYear() - 16); // Subtract 16 years\n        return dobDate <= minDate;\n    }, {\n        message: \"You must be at least 16 years old\"\n    }),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_22__.z.string().min(6, \"Confirm Password must be at least 6 characters long\")\n}).refine((data)=>data.password === data.confirmPassword, {\n    path: [\n        \"confirmPassword\"\n    ],\n    message: \"Passwords do not match\"\n}).refine((data)=>{\n    if (!data.dob) return true; // Skip check if DOB is not provided\n    const dobDate = new Date(data.dob);\n    const today = new Date();\n    const age = getAgeWorkExperienceDifference(today, dobDate);\n    return data.workExperience <= age;\n}, {\n    path: [\n        \"workExperience\"\n    ],\n    message: \"Work experience cannot be greater than your age\"\n});\nfunction FreelancerPage() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // const steps = [\n    //   {\n    //     title: 'Account Details',\n    //     description: 'Basic information',\n    //     icon: <UserCircle className=\"w-6 h-6\" />,\n    //   },\n    //   {\n    //     title: 'Company Info',\n    //     description: 'About your business',\n    //     icon: <Building2 className=\"w-6 h-6\" />,\n    //   },\n    //   {\n    //     title: 'Verification',\n    //     description: 'Contact details',\n    //     icon: <Shield className=\"w-6 h-6\" />,\n    //   },\n    // ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex w-full items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-5xl px-4 sm:px-6 lg:px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Stepper, {\n                    currentStep: currentStep\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                    lineNumber: 275,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-4xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FreelancerRegisterForm, {\n                            currentStep: currentStep,\n                            setCurrentStep: setCurrentStep\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n            lineNumber: 274,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, this);\n}\n_s(FreelancerPage, \"1sJm2lQ2mRX7Y0EEARB7TDldOEM=\");\n_c1 = FreelancerPage;\nfunction FreelancerRegisterForm(param) {\n    let { currentStep, setCurrentStep } = param;\n    _s1();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"IN\");\n    const [phone, setPhone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChecked, setIsChecked] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // State for checkbox\n    const [Isverified, setIsVerified] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [isTermsDialog, setIsTermsDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastCheckedUsername, setLastCheckedUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const togglePasswordVisibility = ()=>{\n        setShowPassword((prev)=>!prev);\n    };\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_23__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(profileFormSchema),\n        defaultValues: {\n            firstName: \"\",\n            lastName: \"\",\n            email: \"\",\n            userName: \"\",\n            phone: \"\",\n            githubLink: \"\",\n            linkedin: \"\",\n            personalWebsite: \"\",\n            password: \"\",\n            perHourPrice: 0,\n            workExperience: 0,\n            referralCode: \"\",\n            dob: \"\"\n        },\n        mode: \"all\"\n    });\n    const handlePreviousStep = async ()=>{\n        setCurrentStep(currentStep - 1);\n    };\n    const handleNextStep = async ()=>{\n        if (currentStep === 0) {\n            const isValid = await form.trigger([\n                \"firstName\",\n                \"lastName\",\n                \"email\",\n                \"dob\",\n                \"userName\",\n                \"password\",\n                \"confirmPassword\"\n            ]);\n            if (isValid) {\n                setCurrentStep(currentStep + 1);\n            } else {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    variant: \"destructive\",\n                    title: \"Validation Error\",\n                    description: \"Please fill in all required fields before proceeding.\"\n                });\n            }\n        } else if (currentStep === 1) {\n            const isValid = await form.trigger([\n                \"githubLink\",\n                \"linkedin\",\n                \"personalWebsite\",\n                \"perHourPrice\",\n                \"resume\",\n                \"workExperience\"\n            ]);\n            if (isValid) {\n                const { userName } = form.getValues();\n                setIsVerified(true);\n                try {\n                    const username = userName;\n                    if (username === lastCheckedUsername) {\n                        setCurrentStep(currentStep + 1);\n                        return;\n                    }\n                    const response = await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_10__.axiosInstance.get(\"/public/username/check-duplicate?username=\".concat(username, \"&is_freelancer=true\"));\n                    if (response.data.duplicate === false) {\n                        setCurrentStep(currentStep + 1);\n                    } else {\n                        (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                            variant: \"destructive\",\n                            title: \"User Already Exists\",\n                            description: \"This username is already taken. Please choose another one.\"\n                        });\n                        setLastCheckedUsername(username);\n                    }\n                } catch (error) {\n                    (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                        variant: \"destructive\",\n                        title: \"API Error\",\n                        description: \"There was an error while checking the username.\"\n                    });\n                } finally{\n                    setIsVerified(false);\n                }\n            } else {\n                (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                    variant: \"destructive\",\n                    title: \"Validation Error\",\n                    description: \"Please fill in all required fields before proceeding.\"\n                });\n            }\n        }\n    };\n    const onSubmit = async (data)=>{\n        var _countries_find, _countries_find1;\n        const referralCodeFromQuery = searchParams.get(\"referral\");\n        const referralCodeFromForm = data.referralCode;\n        const referralCode = referralCodeFromQuery || referralCodeFromForm || null;\n        setPhone(\"\".concat((_countries_find = _country_codes_json__WEBPACK_IMPORTED_MODULE_5__.find((c)=>c.code === code)) === null || _countries_find === void 0 ? void 0 : _countries_find.dialCode).concat(data.phone));\n        setIsLoading(true);\n        const formData = {\n            ...data,\n            phone: \"\".concat((_countries_find1 = _country_codes_json__WEBPACK_IMPORTED_MODULE_5__.find((c)=>c.code === code)) === null || _countries_find1 === void 0 ? void 0 : _countries_find1.dialCode).concat(data.phone),\n            phoneVerify: false,\n            role: \"freelancer\",\n            connects: 0,\n            professionalInfo: {},\n            skills: [],\n            domain: [],\n            education: {},\n            projects: {},\n            isFreelancer: true,\n            refer: {\n                name: \"string\",\n                contact: \"string\"\n            },\n            pendingProject: [],\n            rejectedProject: [],\n            acceptedProject: [],\n            oracleProject: [],\n            userDataForVerification: [],\n            interviewsAligned: [],\n            // oracleStatus: 'notApplied',\n            dob: data.dob ? new Date(data.dob).toISOString() : null\n        };\n        const url = referralCode ? \"/register/freelancer?referralCode=\".concat(referralCode) : \"/register/freelancer\";\n        try {\n            await _lib_axiosinstance__WEBPACK_IMPORTED_MODULE_10__.axiosInstance.post(url, formData);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                title: \"Account created successfully!\",\n                description: \"Redirecting to login page...\"\n            });\n            setIsModalOpen(true);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Something went wrong!\";\n            console.error(\"API Error:\", error);\n            (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_11__.toast)({\n                variant: \"destructive\",\n                title: \"Uh oh! Something went wrong.\",\n                description: errorMessage,\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_24__.ToastAction, {\n                    altText: \"Try again\",\n                    children: \"Try again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 17\n                }, this)\n            });\n        } finally{\n            setTimeout(()=>setIsLoading(false), 100);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.Form, {\n        ...form,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: form.handleSubmit(onSubmit),\n            className: \"w-full max-w-3xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full p-4 sm:p-6 rounded-lg shadow-sm border\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 sm:gap-6 w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"grid gap-4\", currentStep === 0 ? \"\" : \"hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 sm:grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            control: form.control,\n                                            name: \"firstName\",\n                                            label: \"First Name\",\n                                            placeholder: \"Max\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            control: form.control,\n                                            name: \"lastName\",\n                                            label: \"Last Name\",\n                                            placeholder: \"Robinson\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 sm:grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            control: form.control,\n                                            name: \"email\",\n                                            label: \"Email\",\n                                            placeholder: \"<EMAIL>\",\n                                            type: \"email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col gap-2 mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Date of Birth\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_23__.Controller, {\n                                                    control: form.control,\n                                                    name: \"dob\",\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DateOfBirthPicker_DateOfBirthPicker__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            field: field\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 44\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    control: form.control,\n                                    name: \"userName\",\n                                    label: \"Username\",\n                                    placeholder: \"JohnDoe123\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                            control: form.control,\n                                            name: \"password\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        placeholder: \"Enter your password\",\n                                                                        type: showPassword ? \"text\" : \"password\",\n                                                                        className: \"pr-10\",\n                                                                        ...field\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                        lineNumber: 537,\n                                                                        columnNumber: 27\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: togglePasswordVisibility,\n                                                                        className: \"absolute inset-y-0 right-0 px-3 flex items-center\",\n                                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 31\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormField, {\n                                            control: form.control,\n                                            name: \"confirmPassword\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormItem, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormControl, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                                                                        placeholder: \"Confirm your password\",\n                                                                        type: showPassword ? \"text\" : \"password\",\n                                                                        className: \"pr-10\",\n                                                                        ...field\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 27\n                                                                    }, void 0),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: togglePasswordVisibility,\n                                                                        className: \"absolute inset-y-0 right-0 px-3 flex items-center\",\n                                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                            lineNumber: 583,\n                                                                            columnNumber: 31\n                                                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-4 w-4 sm:h-5 sm:w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 31\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_13__.FormMessage, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 justify-end mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                        type: \"button\",\n                                        onClick: handleNextStep,\n                                        className: \"w-full sm:w-auto flex items-center justify-center\",\n                                        disabled: Isverified,\n                                        children: Isverified ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            size: 20,\n                                            className: \"animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                \"Next\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"w-4 h-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"grid gap-4\", currentStep === 1 ? \"\" : \"hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 sm:grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            control: form.control,\n                                            name: \"githubLink\",\n                                            label: \"GitHub\",\n                                            type: \"url\",\n                                            placeholder: \"https://github.com/yourusername\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            control: form.control,\n                                            name: \"referralCode\",\n                                            label: \"Referral\",\n                                            type: \"string\",\n                                            placeholder: \"JOHN123\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 sm:grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            control: form.control,\n                                            name: \"linkedin\",\n                                            label: \"LinkedIn\",\n                                            type: \"url\",\n                                            placeholder: \"https://linkedin.com/in/yourprofile\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 641,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            control: form.control,\n                                            name: \"personalWebsite\",\n                                            label: \"Personal Website\",\n                                            type: \"url\",\n                                            placeholder: \"https://www.yourwebsite.com\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 649,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 sm:grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            control: form.control,\n                                            name: \"perHourPrice\",\n                                            label: \"Hourly Rate ($)\",\n                                            type: \"number\",\n                                            placeholder: \"0\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            control: form.control,\n                                            name: \"resume\",\n                                            label: \"Resume (URL)\",\n                                            type: \"url\",\n                                            placeholder: \"Enter Google Drive Resume Link\",\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid gap-4 sm:grid-cols-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_input__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        control: form.control,\n                                        name: \"workExperience\",\n                                        label: \"Work Experience (Years)\",\n                                        type: \"number\",\n                                        placeholder: \"0\",\n                                        className: \"w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 justify-between mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            type: \"button\",\n                                            onClick: handlePreviousStep,\n                                            className: \"w-full sm:w-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Previous\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 692,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            type: \"button\",\n                                            onClick: handleNextStep,\n                                            className: \"w-full sm:w-auto\",\n                                            children: [\n                                                \"Next\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    className: \"w-4 h-4 ml-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"grid gap-4\", currentStep === 2 ? \"\" : \"hidden\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                            htmlFor: \"phone\",\n                                            children: \"Phone Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_phoneNumberChecker__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            control: form.control,\n                                            setCode: setCode,\n                                            code: code\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"terms\",\n                                            checked: isChecked,\n                                            onChange: ()=>{\n                                                if (!isTermsDialog) {\n                                                    setIsChecked(!isChecked);\n                                                }\n                                            },\n                                            className: \"rounded border-gray-300 text-primary focus:ring-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"terms\",\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    onClick: ()=>setIsTermsDialog(true),\n                                                    className: \"text-primary hover:underline\",\n                                                    children: \"Terms and Conditions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 738,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 736,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_FreelancerTermsDialog__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            open: isTermsDialog,\n                                            setOpen: setIsTermsDialog,\n                                            setIsChecked: setIsChecked\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 745,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 flex-col sm:flex-row justify-between mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            type: \"button\",\n                                            onClick: handlePreviousStep,\n                                            className: \"w-full sm:w-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 758,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Previous\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 753,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                            type: \"submit\",\n                                            className: \"w-full sm:w-auto\",\n                                            disabled: isLoading || !isChecked,\n                                            children: [\n                                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 767,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Briefcase_CheckCircle2_Eye_EyeOff_Loader2_LoaderCircle_Rocket_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                                    lineNumber: 769,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Create account\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                            lineNumber: 761,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                            lineNumber: 712,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_otpDialog__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            phoneNumber: phone,\n                            isModalOpen: isModalOpen,\n                            setIsModalOpen: setIsModalOpen\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n                lineNumber: 477,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n            lineNumber: 473,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Dehix\\\\dehix_alpha_frontend\\\\src\\\\components\\\\form\\\\register\\\\freelancer.tsx\",\n        lineNumber: 472,\n        columnNumber: 5\n    }, this);\n}\n_s1(FreelancerRegisterForm, \"8oR2rRQb+FrVupOY+4qUUEYoq/c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_23__.useForm\n    ];\n});\n_c2 = FreelancerRegisterForm;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Stepper\");\n$RefreshReg$(_c1, \"FreelancerPage\");\n$RefreshReg$(_c2, \"FreelancerRegisterForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/form/register/freelancer.tsx\n"));

/***/ })

});